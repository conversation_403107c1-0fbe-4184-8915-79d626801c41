import os
import yaml
from envyaml import EnvYAML


def load_config(config_path):
    with open(config_path, "r", encoding="utf-8-sig") as file:
        return yaml.safe_load(file)


def load_config_with_env(config_path):
    env_yaml = EnvYAML(config_path, strict=False)
    return env_yaml


# config = load_config("./env.yaml")
config = load_config_with_env("env.yaml")

if __name__ == "__main__":
    print(config)
    print(config["selected_llm_server"])
    print(config["llm_server_set.senseauto"])

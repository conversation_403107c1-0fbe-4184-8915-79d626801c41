stages:
  - extract_version
  - build_image
  - deploy_sit
#  - deploy_prd

variables:
  K8S_SERVICE_NAME:
    value: "gac-agent-traveler-service"
    description: "The service name of K8S"
  ECR_REPO_NAME:
    value: "cabin-agent-traveler-service"
    description: "The repository name of Elastic Container Repository, will create one if not exist"
  IMAGE_TAG_OVERWRITE:
    value: ""
    description: "Overwrite docker image version defined in this project"

extract_version_job:
  stage: extract_version
  image:
    name: alpine/git
#    entrypoint: [""]
  script:
    - GIT_TAG=`git describe --tags --abbrev=0 --always | sed 's/^V//' | sed 's/^v//'`
    - echo $GIT_TAG
    - echo "IMAGE_VERSION=$GIT_TAG" >> build.env
    - echo "PROJECT_VERSION=$GIT_TAG" >> build.env
  tags: # tags 参数，用于选择执行 job 的 runner
    - k8s                                  # job 会在标记为 k8s 的 runner 上运行
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: always

build_and_push_image_job:
  stage: build_image
  image:                                   # 设置运行此 job 使用的 docker image
    name: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws    # 用于执行这个 job 用到的 docker image，即 script 中的命令是在基于这个 docker image 启动的 container 中执行的
    entrypoint: [ "" ]
  tags: # tags 参数，用于选择执行 job 的 runner
    - docker                                  # job 会在标记为 docker 的 runner 上运行dind 功能
  script:
    - if [ -z ${IMAGE_TAG_OVERWRITE} ]; then CUSTOM_TAG=${PROJECT_VERSION}; fi
    - if [ ! -z ${IMAGE_TAG_OVERWRITE} ]; then CUSTOM_TAG=${IMAGE_TAG_OVERWRITE}; fi
    - echo $CUSTOM_TAG
    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
    - echo "PROJECT_VERSION=$PROJECT_VERSION" >> build.env
    - docker login registry.sensetime.com -u $username -p $password

    - aws ecr get-login-password --region cn-northwest-1 | docker login --username AWS --password-stdin 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn
    - aws ecr describe-repositories --repository-names $ECR_REPO_NAME --region cn-northwest-1 || aws ecr create-repository --repository-name $ECR_REPO_NAME --region cn-northwest-1 # 如果不存在则创建repository
    - docker build -t 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG .
    - docker push 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG

    - aws ecr get-login-password --region cn-north-1 | docker login --username AWS --password-stdin 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn
    - aws ecr describe-repositories --repository-names $ECR_REPO_NAME --region cn-north-1 || aws ecr create-repository --repository-name $ECR_REPO_NAME --region cn-north-1 # 如果不存在则创建repository
    - docker build -t 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG .
    - docker push 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: always
  artifacts:
    reports:
      dotenv: build.env


deploy_k8s_sit:
  needs:
    - build_and_push_image_job
  stage: deploy_sit
  image: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws
  script:
    - echo "Deploy $K8S_SERVICE_NAME service to auto-cloud-sit cluster"
    - kubectx auto-cloud-sit
    - kubectx -c
    - echo "CUSTOM_TAG=$CUSTOM_TAG"
    - helm upgrade --install $K8S_SERVICE_NAME ./pipeline -f pipeline/values/auto-cloud-sit/values.yaml --reset-values --set application.version=${CUSTOM_TAG} --set image.tag=${CUSTOM_TAG}
    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
      when: manual
      allow_failure: false
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: manual
      allow_failure: false
  artifacts:
    reports:
      dotenv: build.env

#deploy_k8s_prd:
#  needs:
#    - deploy_k8s_sit
#  stage: deploy_prd
#  image: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws
#  script:
#    - echo "Deploy $K8S_SERVICE_NAME service to auto-cloud-prd cluster"
#    - kubectx auto-cloud-prd
#    - kubectx -c
#    - echo "CUSTOM_TAG=$CUSTOM_TAG"
#    - helm upgrade --install $K8S_SERVICE_NAME ./pipeline -f pipeline/values/auto-cloud-prd/values.yaml --reset-values --set application.version=${CUSTOM_TAG} --set image.tag=${CUSTOM_TAG}
#    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
#  rules:
#    - if: '$CI_PIPELINE_SOURCE == "web"'
#      when: manual
#      allow_failure: false
#    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
#      when: manual
#      allow_failure: false
#  artifacts:
#    reports:
#      dotenv: build.env
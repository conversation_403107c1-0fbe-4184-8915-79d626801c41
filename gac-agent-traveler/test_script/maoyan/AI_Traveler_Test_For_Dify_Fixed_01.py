import requests
import time
import json
import os
import pandas as pd

# ===== 配置区域 =====
DIFY_TTFB_THRESHOLD = 3
TTFT_THRESHOLD = 3
T1_LOC_THRESHOLD = 0.1
T2_MEM_THRESHOLD = 0.1
T3_PRE_INTENT_THRESHOLD = 1
T3_INTENT_THRESHOLD = 1
T4_SRC_THRESHOLD = 2
T5_FETCH_THRESHOLD = 1
T6_MFT_THRESHOLD = 0.5
T7_SFT_THRESHOLD = 0.5
T8_LIST_THRESHOLD = 1.5
T9_BRIEF_THRESHOLD = 1
T10_DETAIL_THRESHOLD = 10
T_ACFT_THRESHOLD = 3

# 修正后的URL - 使用正确的端点
url = "http://0.0.0.0:8080/gac-agent-traveler/v1/ai_traveler"
headers = {
    "Content-Type": "application/json"
}

# 简化的测试查询
queries = [
    "周末两天有空，适合短途旅行去哪里？",
    "西北有什么好玩的地方？",
    "苏州有什么值得去的地方？"
]

test_time = time.strftime("%Y%m%d_%H%M%S", time.localtime())

# ===== 开始测试 =====
results = []

def test_health_endpoint():
    """测试健康检查端点"""
    health_url = "http://0.0.0.0:8080/gac-agent-traveler/v1/health"
    try:
        response = requests.get(health_url, timeout=5)
        print(f"🏥 健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"🏥 健康检查响应: {response.json()}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_simple_request():
    """测试简单的AI旅行家请求"""
    payload = {
        "query": "北京有什么好玩的地方？",
        "stream": False,  # 先测试非流式
        "k": 5,
        "detect": False,
        "use_search_cache": True,
        "engine": "tencent",
        "location": {
            "lat": "39.9042",
            "lon": "116.4074"
        },
        "user_info": {
            "car_id": "test_car_id",
            "user_id": "test_user_id",
            "category": ["旅游", "景点"]
        }
    }
    
    try:
        print(f"🚀 测试简单请求...")
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"📊 状态码: {response.status_code}")
        print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📝 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)[:500]}...")
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

# 执行测试
print("=" * 50)
print("🧪 开始AI旅行家测试")
print("=" * 50)

# 1. 健康检查
print("\n1. 健康检查测试")
health_ok = test_health_endpoint()

if health_ok:
    # 2. 简单请求测试
    print("\n2. 简单请求测试")
    simple_ok = test_simple_request()
    
    if simple_ok:
        print("\n✅ 基础测试通过，可以继续进行完整测试")
    else:
        print("\n❌ 简单请求测试失败，请检查API接口")
else:
    print("\n❌ 健康检查失败，请检查服务状态")

print("\n" + "=" * 50)
print("🏁 测试完成")
print("=" * 50)

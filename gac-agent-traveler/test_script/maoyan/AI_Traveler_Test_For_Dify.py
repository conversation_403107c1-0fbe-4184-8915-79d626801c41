import requests
import time
import json
import os
import pandas as pd
# ===== 配置区域 =====

DIFY_TTFB_THRESHOLD = 3
TTFT_THRESHOLD = 3
T1_LOC_THRESHOLD = 0.1
T2_MEM_THRESHOLD = 0.1
T3_PRE_INTENT_THRESHOLD = 1
T3_INTENT_THRESHOLD = 1
T4_SRC_THRESHOLD = 2
T5_FETCH_THRESHOLD = 1
T6_MFT_THRESHOLD = 0.5
T7_SFT_THRESHOLD = 0.5
T8_LIST_THRESHOLD = 1.5
T9_BRIEF_THRESHOLD = 1
T10_DETAIL_THRESHOLD = 10
T_ACFT_THRESHOLD = 3

url = "http://0.0.0.0:8080/v1/chat-messages"
# url = "https://agent-sit.senseauto.com/v1/chat-messages"
headers = {
    "Authorization": "Bearer app-C3QVJnAT53fkN8SUJgItY8ag",
    "Content-Type": "application/json"
}

queries = [
    "周末两天有空，适合短途旅行去哪里？",
    "西北有什么好玩的地方？",
    "周末两天时间，想就近休闲度假，有推荐的吗？",
    "内蒙有什么景点推荐？",
    "苏州有什么值得去的地方？",
    "中秋节三天想去看自然风景，有哪些好去处？",
    "冬天适合去什么地方",
    "我想带小孩去爬山，哪里适合去？",
    "明天请了一天假，想去逛街，给我推荐一下哪里适合逛街？",
    "放假两天想看海，有推荐的海边目的地吗？",
    "现在这里有啥好玩的地方？",
    "我想去博物馆"
]
test_time = time.strftime("%Y%m%d_%H%M%S", time.localtime())
desktop_path = os.path.expanduser("~/Desktop")
output_file = os.path.join(desktop_path, "AI旅行家_Dify_20250712_03.txt")

# ===== 开始测试 =====

results = []

excelData = {
    'query': [],
    'Dify-TTFB': [],
    'TTFT': [],
    't1-loc': [],
    't2-mem': [],
    't3-pre-intent': [],
    't3-intent': [],
    't4-src': [],
    't5-fetch': [],
    't6-mft': [],
    't7-sft': [],
    't8-list': [],
    't9-brief': [],
    't10-detail': [],
    't-acft': [],
    'Answer': [],
    'Remark': []
}




def fill_excel_data():
    ttft = float(sense_time_info.get("TTFT", "")) if type(sense_time_info) is dict else 0
    t_acft = float(sense_time_info.get("t-acft", "")) if type(sense_time_info) is dict else 0
    t1_loc = float(sense_time_info.get("t1-loc", "")) if type(sense_time_info) is dict else 0
    t2_mem = float(sense_time_info.get("t2-mem", "")) if type(sense_time_info) is dict else 0
    t3_pre_intent = float(sense_time_info.get("t3-pre-intent", "")) if type(sense_time_info) is dict else 0
    t3_intent = float(sense_time_info.get("t3-intent", "")) if type(sense_time_info) is dict else 0
    t4_src = float(sense_time_info.get("t4-src", "")) if type(sense_time_info) is dict else 0
    t5_fetch = float(sense_time_info.get("t5-fetch", "")) if type(sense_time_info) is dict else 0
    t6_mft = float(sense_time_info.get("t6-mft", "")) if type(sense_time_info) is dict else 0
    t7_sft = float(sense_time_info.get("t7-sft", "")) if type(sense_time_info) is dict else 0
    t8_list = float(sense_time_info.get("t8-list", "")) if type(sense_time_info) is dict else 0
    t9_brief = float(sense_time_info.get("t9-brief", "")) if type(sense_time_info) is dict else 0
    t10_detail = float(sense_time_info.get("t10-detail", 0)) if type(sense_time_info) is dict else 0

    ttfb_value = float(ttfb_display) if ttfb_display != "N/A" else 0
    t2_mem_display = t2_mem - t1_loc
    t3_pre_intent_display = t3_pre_intent - t2_mem
    t3_intent_display = t3_intent - t3_pre_intent
    t4_src_display = t4_src - t3_intent
    t5_fetch_display = t5_fetch - t4_src
    t6_mft_display = t6_mft - t5_fetch
    t7_sft_display = t7_sft - t6_mft
    t8_list_display = t8_list - t6_mft
    t9_brief_display = t9_brief - t8_list
    t10_detail_display = t10_detail - t9_brief

    failed_columns = []
    if ttfb_display == "N/A":
        failed_columns.append(f"Dify-TTFB为N/A")
    elif ttfb_value >= DIFY_TTFB_THRESHOLD:
        failed_columns.append(f"Dify-TTFB超过{DIFY_TTFB_THRESHOLD}")
    if ttft >= TTFT_THRESHOLD:
        failed_columns.append(f"TTFT超过{TTFT_THRESHOLD}")
    if t1_loc >= T1_LOC_THRESHOLD:
        failed_columns.append(f"t1-loc超过{T1_LOC_THRESHOLD}")
    if t2_mem_display >= T2_MEM_THRESHOLD:
        failed_columns.append(f"t2-mem超过{T2_MEM_THRESHOLD}")
    if t3_pre_intent_display >= T3_PRE_INTENT_THRESHOLD:
        failed_columns.append(f"t3-pre-intent超过{T3_PRE_INTENT_THRESHOLD}")
    if t3_intent_display >= T3_INTENT_THRESHOLD:
        failed_columns.append(f"t3-intent超过{T3_INTENT_THRESHOLD}")
    if t4_src_display >= T4_SRC_THRESHOLD:
        failed_columns.append(f"t4-src超过{T4_SRC_THRESHOLD}")
    if t5_fetch_display >= T5_FETCH_THRESHOLD:
        failed_columns.append(f"t5-fetch超过{T5_FETCH_THRESHOLD}")
    if t6_mft_display >= T6_MFT_THRESHOLD:
        failed_columns.append(f"t6-mft超过{T6_MFT_THRESHOLD}")
    if t7_sft_display >= T7_SFT_THRESHOLD:
        failed_columns.append(f"t7-sft超过{T7_SFT_THRESHOLD}")
    if t8_list_display >= T8_LIST_THRESHOLD:
        failed_columns.append(f"t8-list超过{T8_LIST_THRESHOLD}")
    if t9_brief_display >= T9_BRIEF_THRESHOLD:
        failed_columns.append(f"t9-brief超过{T9_BRIEF_THRESHOLD}")
    if t10_detail_display >= T10_DETAIL_THRESHOLD:
        failed_columns.append(f"t10-detail超过{T10_DETAIL_THRESHOLD}")
    if t_acft >= T_ACFT_THRESHOLD:
        failed_columns.append(f"t-acft超过{T_ACFT_THRESHOLD}")

    view_count = final_answer.count('"view_name"') if final_answer and final_answer != "无有效响应" else 0
    
    remark_parts = []
    if failed_columns:
        remark_parts.append("; ".join(failed_columns))
    
    remark_parts.append(f"view数量={view_count}")

    remark = "; ".join(remark_parts) if remark_parts else ""

    excelData['query'].append(query_text)
    excelData['Dify-TTFB'].append(ttfb_display)
    excelData['TTFT'].append(ttft)
    excelData['t1-loc'].append(t1_loc)
    excelData['t2-mem'].append(t2_mem - t1_loc)
    excelData['t3-pre-intent'].append(t3_pre_intent - t2_mem)
    excelData['t3-intent'].append(t3_intent - t3_pre_intent)
    excelData['t4-src'].append(t4_src - t3_intent)
    excelData['t5-fetch'].append(t5_fetch - t4_src)
    excelData['t6-mft'].append(t6_mft - t5_fetch)
    excelData['t7-sft'].append(round(t7_sft - t6_mft, 6))
    excelData['t8-list'].append(t8_list - t6_mft)
    excelData['t9-brief'].append(t9_brief - t8_list)
    excelData['t10-detail'].append(t10_detail - t9_brief)
    excelData['t-acft'].append(t_acft)
    excelData['Answer'].append(final_answer)
    excelData['Remark'].append(remark)

def add_params_des():
    excelData['query'].append(" ")
    excelData['Dify-TTFB'].append("Dify智能体接口首字延迟")
    excelData['TTFT'].append("tts首字首字延迟")
    excelData['t1-loc'].append("高德经纬度转换成城市")
    excelData['t2-mem'].append("获取用户画像")
    excelData['t3-pre-intent'].append("前置落域")
    excelData['t3-intent'].append("意图识别")
    excelData['t4-src'].append("搜索引擎返回结果")
    excelData['t5-fetch'].append("爬虫结束")
    excelData['t6-mft'].append("大模型总结首字延迟")
    excelData['t7-sft'].append("安全检测首字延迟")
    excelData['t8-list'].append("生成景点列表的时间")
    excelData['t9-brief'].append("根据景点列表请求到高德信息的时间")
    excelData['t10-detail'].append("大模型生成完所有景点的详情介绍的时间")
    excelData['t-acft'].append("原子能力首字延迟")
    excelData['Answer'].append(" ")
    excelData['Remark'].append("")


def output_excel():

    df = pd.DataFrame(excelData)
    # 使用ExcelWriter和xlsxwriter引擎
    file_path = 'AI旅行家_'+test_time+'.xlsx'
    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='AI旅行家（tencent）')
    # 获取工作表和workbook对象
    workbook = writer.book
    worksheet = writer.sheets['AI旅行家（tencent）']
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'center',
        'fg_color': '#D7E4BC',
        'border': 1})
    data_format = workbook.add_format({'align': 'center', "valign": "vcenter"})
    # 设置列宽
    worksheet.set_column(0, len(excelData) - 2, 20, data_format)
    worksheet.set_column(len(excelData) - 1, len(excelData) - 1, 60, data_format)
    for row in range(1, len(excelData) - 1):  # 假设设置前 100 行
        worksheet.set_row(row, 30)
    # 应用标题格式（第一行是标题行）
    worksheet.write_row('A1', df.columns, header_format)
    
    yellow_format = workbook.add_format({'bg_color': '#FFFF00'})

    check_columns = {
        'B': 'Dify-TTFB',
        'C': 'TTFT',
        'D': 't1-loc',
        'E': 't2-mem',
        'F': 't3-pre-intent',
        'G': 't3-intent',
        'H': 't4-src',
        'I': 't5-fetch',
        'J': 't6-mft',
        'K': 't7-sft',
        'L': 't8-list',
        'M': 't9-brief',
        'N': 't10-detail',
        'O': 't-acft'
    }

    for col_letter, col_name in check_columns.items():
        start_row = 1
        end_row = len(excelData[col_name]) - 1

        if col_letter == 'B':
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'={col_letter}2="N/A"',
                    'format': yellow_format
                }
            )
            worksheet.conditional_format(
                f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                {
                    'type': 'formula',
                    'criteria': f'=AND(ISNUMBER(VALUE({col_letter}2)), VALUE({col_letter}2)>={DIFY_TTFB_THRESHOLD})',
                    'format': yellow_format
                }
            )
        else:
            threshold_var_name = f"{col_name.replace('-', '_').upper()}_THRESHOLD"
            if threshold_var_name in globals():
                threshold_var = globals()[threshold_var_name]
                worksheet.conditional_format(
                    f'{col_letter}{start_row + 1}:{col_letter}{end_row + 1}',
                    {
                        'type': 'formula',
                        'criteria': f'=AND(ISNUMBER({col_letter}2), {col_letter}2>={threshold_var})',
                        'format': yellow_format
                    }
                )

    writer.close()


conversation_id_to_pass = None

for idx, query_text in enumerate(queries, 1):
    print(f"🚀 正在测试 Query {idx}/{len(queries)}：{query_text}")

    payload = {
        "query": query_text,
        "response_mode": "streaming",
        "inputs": {
            "car_id": "fake_car_id",
            "user_id": "fake_user_id",
            "lat": "31.16813",
            "lon": "121.39987",
            "detect": "false",
            "search_engine": "tencent",
            "user_info": "{\"seats\": [{\"position\": [\"二排左\"], \"faceid\": \"1\", \"name\": \"shawn\", \"age\": \"儿童\", \"gender\": \"男\", \"dress\": [{\"category\": \"连衣裙\"}]}, {\"position\": [\"主驾\"], \"faceid\": \"2\", \"gender\": \"女\", \"name\": \"della\", \"dress\": [{\"category\": \"连衣裙\"}]}]}"
        },
        "user": "abc-124"
    }

    if idx == len(queries) and conversation_id_to_pass:
        payload["conversation_id"] = conversation_id_to_pass
        print(f"💡 正在为最后一条 query 传入 conversation_id: {conversation_id_to_pass}")

    start_time = time.time()
    print(f"✅ start_time ：{start_time}")
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=15)
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        continue

    if response.status_code != 200:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        continue

    # 初始化状态
    first_message_time = None
    final_answer = ""
    sense_time_info = None

    try:
        for raw_chunk in response.iter_lines(decode_unicode=True):
            if not raw_chunk.strip():
                continue

            if raw_chunk.startswith("data: "):
                json_str = raw_chunk[len("data: "):]
                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    continue

                if idx == len(queries) - 1: # 仅在倒数第二个用例时执行
                    if data.get("event") == "workflow_started" and "conversation_id" in data:
                        conversation_id_to_pass = data.get("conversation_id")
                        print(f"⭐ 成功捕获 conversation_id: {conversation_id_to_pass}")

                event_type = data.get("event")

                if event_type == "message":
                    if first_message_time is None:
                        first_message_time = time.time()
                        print(f"✅ first_message_time ：{first_message_time}")
                        ttfb = round((first_message_time - start_time) , 4)

                    if "answer" in data:
                        try:
                            answer_obj = json.loads(data["answer"])
                            text = answer_obj.get("data", "")
                            final_answer += text
                        except json.JSONDecodeError:
                            continue

                elif event_type == "agent_log":
                    log_data = data.get("data")
                    if isinstance(log_data, dict) and log_data.get("label") == "Sense_Time_Info":
                        sense_time_info = log_data.get("data")

    except Exception as e:
        print(f"⚠️ 流读取异常: {e}")
        continue

    # 输出并保存结果
    ttfb_display = str(round((first_message_time - start_time), 4)) if first_message_time else "N/A"
    final_answer = final_answer.strip() or "无有效响应"
    sense_time_str = json.dumps(sense_time_info, ensure_ascii=False) if sense_time_info else "无"

    result_text = f"\n\nQuery: {query_text}\nTTFB(ms): {ttfb_display}\nSense_Time_Info: {sense_time_str}\nFinal Answer: {final_answer}"
    results.append(result_text)
    fill_excel_data()

add_params_des()
output_excel()
print("✅ 写入完成\n")

# ===== 写入文件 =====

# try:
#     with open(output_file, "w", encoding="utf-8") as f:
#         f.write("\n".join(results))
#     print(f"📄 测试结果已保存到：{output_file}")
# except Exception as e:
#     print(f"❌ 写文件失败: {e}")

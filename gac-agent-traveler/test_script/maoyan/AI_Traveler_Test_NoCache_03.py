import requests
import time
import json

# 禁用缓存的测试脚本
url = "http://0.0.0.0:8080/gac-agent-traveler/v1/ai_traveler"
headers = {
    "Content-Type": "application/json"
}

def test_no_cache_request():
    """测试禁用缓存的AI旅行家请求"""
    payload = {
        "query": "北京有什么好玩的地方？",
        "stream": True,  # 使用流式响应
        "k": 2,  # 减少搜索结果数量
        "detect": False,
        "use_search_cache": False,  # 禁用搜索缓存
        "engine": "tencent",
        "location": {
            "lat": "39.9042",
            "lon": "116.4074"
        },
        "user_info": {
            "car_id": "test_car_id",
            "user_id": "test_user_id",
            "category": ["旅游"]
        }
    }
    
    try:
        print(f"🚀 开始禁用缓存的流式请求测试...")
        start_time = time.time()
        
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=180)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 开始接收流式响应...")
            
            chunk_count = 0
            first_chunk_time = None
            message_chunks = []
            
            for line in response.iter_lines(decode_unicode=True):
                if not line.strip():
                    continue
                    
                chunk_count += 1
                current_time = time.time()
                
                if first_chunk_time is None:
                    first_chunk_time = current_time
                    ttfb = current_time - start_time
                    print(f"⚡ TTFB (首字节时间): {ttfb:.2f}秒")
                
                if line.startswith("data: "):
                    data_str = line[6:]  # 去掉 "data: " 前缀
                    try:
                        data = json.loads(data_str)
                        event_type = data.get("type", "unknown")
                        
                        # 打印关键事件
                        if event_type in ["pre_intent", "t3-intent", "t4-src", "t5-fetch", "t6-mft", "t7-sft", "message", "messageEnd"]:
                            elapsed = current_time - start_time
                            print(f"📝 [{elapsed:.2f}s] {event_type}: {str(data)[:100]}...")
                            
                        # 收集消息内容
                        if event_type == "message":
                            message_data = data.get("data", "")
                            message_chunks.append(message_data)
                            
                        # 如果是消息结束，退出
                        if event_type == "messageEnd":
                            total_time = current_time - start_time
                            print(f"🏁 流式响应完成，总耗时: {total_time:.2f}秒")
                            print(f"📊 总chunk数: {chunk_count}")
                            
                            # 打印完整消息
                            full_message = "".join(message_chunks)
                            if full_message:
                                print(f"📄 完整响应内容:")
                                print("-" * 50)
                                print(full_message[:500] + ("..." if len(full_message) > 500 else ""))
                                print("-" * 50)
                            
                            return True
                            
                    except json.JSONDecodeError:
                        print(f"⚠️ JSON解析失败: {data_str[:50]}...")
                        continue
                        
                # 超时保护
                if current_time - start_time > 180:
                    print(f"⏰ 超时退出 (180秒)")
                    break
                    
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

# 执行测试
print("=" * 60)
print("🧪 AI旅行家禁用缓存测试")
print("=" * 60)

success = test_no_cache_request()

if success:
    print("\n✅ 禁用缓存测试完成")
else:
    print("\n❌ 禁用缓存测试失败")

print("=" * 60)
